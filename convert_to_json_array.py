#!/usr/bin/env python3
import json

# Read the file with one JSON object per line
with open('wiki_files_export.json', 'r', encoding='utf-8') as f:
    lines = f.readlines()

# Parse each line as JSON
wiki_files = []
for line in lines:
    line = line.strip()
    if line:
        try:
            wiki_file = json.loads(line)
            wiki_files.append(wiki_file)
        except json.JSONDecodeError as e:
            print(f"Error parsing line: {e}")
            print(f"Line: {line[:100]}...")

# Write the array to a new file
with open('wiki_files_array.json', 'w', encoding='utf-8') as f:
    json.dump(wiki_files, f, ensure_ascii=False, indent=2)

print(f"Converted {len(wiki_files)} wiki files to JSON array")
