#!/usr/bin/env python3
import subprocess
import json
import requests
import time

# Supabase configuration
SUPABASE_URL = "https://namplnzyrvdvskwnzpcu.supabase.co"
SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5hbXBsbnp5cnZkdnNrd256cGN1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjA5ODQ5MCwiZXhwIjoyMDY3Njc0NDkwfQ.sm8w-MJxL-uM-u7bYwLph-4_Dbm_eryer-XIDO9dDic"

def get_wiki_files_count():
    """Get count of wiki files from local database"""
    try:
        result = subprocess.run([
            'psql', 
            'postgresql://postgres:postgres@127.0.0.1:54322/postgres',
            '-t', '-c', 'SELECT COUNT(*) FROM wiki_files;'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            return int(result.stdout.strip())
        else:
            print(f"Error getting count: {result.stderr}")
            return 0
    except Exception as e:
        print(f"Error: {e}")
        return 0

def get_wiki_file_by_index(index):
    """Get a single wiki file by index from local database"""
    try:
        sql = f"""
        SELECT row_to_json(t) FROM (
            SELECT id, name, markdown, folder_id, created_at, sort_order, text, audio_url, words 
            FROM wiki_files 
            ORDER BY created_at 
            LIMIT 1 OFFSET {index}
        ) t;
        """
        
        result = subprocess.run([
            'psql', 
            'postgresql://postgres:postgres@127.0.0.1:54322/postgres',
            '-t', '-c', sql
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            json_str = result.stdout.strip()
            if json_str:
                return json.loads(json_str)
        else:
            print(f"Error getting wiki file {index}: {result.stderr}")
        
        return None
    except Exception as e:
        print(f"Error getting wiki file {index}: {e}")
        return None

def import_wiki_file(wiki_file):
    """Import a single wiki file to Supabase"""
    headers = {
        "apikey": SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SERVICE_ROLE_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/wiki_files",
            headers=headers,
            json=[wiki_file],  # Send as array
            timeout=120
        )
        
        if response.status_code == 201:
            return True, None
        else:
            return False, f"HTTP {response.status_code}: {response.text[:200]}"
            
    except Exception as e:
        return False, str(e)

def main():
    print("🔄 Getting wiki files count from local database...")
    total_count = get_wiki_files_count()
    
    if total_count == 0:
        print("❌ No wiki files found in local database")
        return
    
    print(f"📊 Found {total_count} wiki files to import")
    
    imported = 0
    failed = 0
    
    for i in range(total_count):
        print(f"📤 Getting wiki file {i+1}/{total_count}...")
        
        wiki_file = get_wiki_file_by_index(i)
        
        if not wiki_file:
            print(f"❌ Failed to get wiki file {i+1}")
            failed += 1
            continue
        
        print(f"📤 Importing: {wiki_file.get('name', 'Unknown')[:50]}...")
        
        success, error = import_wiki_file(wiki_file)
        
        if success:
            imported += 1
            print(f"✅ Success")
        else:
            failed += 1
            print(f"❌ Failed: {error}")
        
        # Small delay between requests
        time.sleep(1)
        
        # Progress update every 10 files
        if (i + 1) % 10 == 0:
            print(f"📊 Progress: {imported} imported, {failed} failed")
    
    print(f"\n🎉 Import completed!")
    print(f"✅ Successfully imported: {imported}/{total_count} wiki files")
    print(f"❌ Failed: {failed}/{total_count} wiki files")

if __name__ == "__main__":
    main()
