#!/usr/bin/env python3
import csv
import time

import requests

# Supabase configuration
SUPABASE_URL = "https://namplnzyrvdvskwnzpcu.supabase.co"
SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5hbXBsbnp5cnZkdnNrd256cGN1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjA5ODQ5MCwiZXhwIjoyMDY3Njc0NDkwfQ.sm8w-MJxL-uM-u7bYwLph-4_Dbm_eryer-XIDO9dDic"


def read_wiki_files_from_csv(csv_file):
    """Read wiki files from CSV export"""
    wiki_files = []

    # Increase CSV field size limit
    csv.field_size_limit(1000000)

    with open(csv_file, "r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            # Convert empty strings to None
            for key, value in row.items():
                if value == "":
                    row[key] = None
                elif key == "sort_order":
                    row[key] = int(value) if value else 0

            wiki_files.append(row)

    return wiki_files


def import_wiki_files_batch(wiki_files, batch_size=5):
    """Import wiki files in batches"""
    headers = {
        "apikey": SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SERVICE_ROLE_KEY}",
        "Content-Type": "application/json",
    }

    total = len(wiki_files)
    imported = 0
    failed = 0

    for i in range(0, total, batch_size):
        batch = wiki_files[i : i + batch_size]

        try:
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/wiki_files",
                headers=headers,
                json=batch,
                timeout=60,
            )

            if response.status_code == 201:
                imported += len(batch)
                print(
                    f"✅ Imported batch {i // batch_size + 1}: {imported}/{total} wiki files"
                )
            else:
                failed += len(batch)
                print(
                    f"❌ Error importing batch {i // batch_size + 1}: {response.status_code}"
                )
                print(f"Response: {response.text[:200]}...")

        except Exception as e:
            failed += len(batch)
            print(f"❌ Exception importing batch {i // batch_size + 1}: {e}")

        # Small delay between batches
        time.sleep(1)

    return imported, failed


if __name__ == "__main__":
    print("Reading wiki_files from CSV...")
    wiki_files = read_wiki_files_from_csv("wiki_files_export.csv")
    print(f"Found {len(wiki_files)} wiki files")

    if wiki_files:
        print("Starting import...")
        imported, failed = import_wiki_files_batch(wiki_files, batch_size=3)
        print("\n📊 Import completed:")
        print(f"✅ Successfully imported: {imported}/{len(wiki_files)} wiki files")
        print(f"❌ Failed: {failed}/{len(wiki_files)} wiki files")
    else:
        print("No wiki files found to import")
