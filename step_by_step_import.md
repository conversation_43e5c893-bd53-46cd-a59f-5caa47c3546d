# Пошаговый импорт данных в продакшн Supabase

## Порядок импорта

Важно импортировать данные в правильном порядке из-за внешних ключей:

1. **chats** (независимая таблица)
2. **messages** (зависит от chats)
3. **attachments** (зависит от messages)
4. **news** (независимая таблица)
5. **wiki_folders** (может иметь циклические ссылки)
6. **wiki_files** (зависит от wiki_folders)

## Шаг 1: Импорт чатов

Откройте SQL Editor: https://supabase.com/dashboard/project/namplnzyrvdvskwnzpcu/sql

Скопируйте и выполните содержимое файла `import_chats.sql`

## Шаг 2: Импорт сообщений

Скопируйте и выполните содержимое файла `import_messages.sql`

## Шаг 3: Импорт вложений

Скопируйте и выполните содержимое файла `import_attachments.sql`

## Шаг 4: Импорт новостей

Скопируйте и выполните содержимое файла `import_news.sql`

## Шаг 5: Импорт папок Wiki (с отключением триггеров)

```sql
-- Отключить триггеры для wiki_folders
ALTER TABLE wiki_folders DISABLE TRIGGER ALL;
```

Затем скопируйте и выполните содержимое файла `import_wiki_folders.sql`

```sql
-- Включить триггеры обратно
ALTER TABLE wiki_folders ENABLE TRIGGER ALL;
```

## Шаг 6: Импорт файлов Wiki

Скопируйте и выполните содержимое файла `import_wiki_files.sql`

## Проверка импорта

После завершения импорта выполните проверочный запрос:

```sql
SELECT 
    'chats' as table_name, 
    COUNT(*) as count 
FROM chats
UNION ALL
SELECT 'messages', COUNT(*) FROM messages
UNION ALL
SELECT 'attachments', COUNT(*) FROM attachments
UNION ALL
SELECT 'news', COUNT(*) FROM news
UNION ALL
SELECT 'wiki_folders', COUNT(*) FROM wiki_folders
UNION ALL
SELECT 'wiki_files', COUNT(*) FROM wiki_files
ORDER BY table_name;
```

## Ожидаемые результаты

Примерное количество записей в каждой таблице:
- chats: ~17 записей
- messages: ~много записей (основная часть данных)
- attachments: ~несколько записей
- news: ~несколько записей
- wiki_folders: ~несколько записей
- wiki_files: ~много записей

## В случае ошибок

Если возникают ошибки с внешними ключами, можно временно отключить проверки:

```sql
-- Отключить проверки внешних ключей
SET session_replication_role = replica;

-- Выполнить импорт данных

-- Включить проверки обратно
SET session_replication_role = DEFAULT;
```
