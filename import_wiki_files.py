#!/usr/bin/env python3
import re
import json
import requests
import time

# Supabase configuration
SUPABASE_URL = "https://namplnzyrvdvskwnzpcu.supabase.co"
SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5hbXBsbnp5cnZkdnNrd256cGN1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjA5ODQ5MCwiZXhwIjoyMDY3Njc0NDkwfQ.sm8w-MJxL-uM-u7bYwLph-4_Dbm_eryer-XIDO9dDic"

def parse_wiki_files_sql(sql_file):
    """Parse wiki_files SQL INSERT statements"""
    wiki_files = []
    
    with open(sql_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find INSERT statements - they can span multiple lines
    pattern = r"INSERT INTO public\.wiki_files VALUES \(([^;]+)\);"
    matches = re.findall(pattern, content, re.DOTALL)
    
    for match in matches:
        # Parse the values - this is complex due to quoted strings with commas
        # Let's use a simple approach for now
        try:
            # Split by comma but handle quoted strings
            parts = []
            current_part = ""
            in_quotes = False
            quote_char = None
            paren_count = 0
            
            i = 0
            while i < len(match):
                char = match[i]
                
                if char == '(' and not in_quotes:
                    paren_count += 1
                elif char == ')' and not in_quotes:
                    paren_count -= 1
                elif not in_quotes and char in ["'"]:
                    in_quotes = True
                    quote_char = char
                elif in_quotes and char == quote_char:
                    # Check if it's escaped
                    if i + 1 < len(match) and match[i + 1] == quote_char:
                        current_part += char + char
                        i += 1
                    else:
                        in_quotes = False
                        quote_char = None
                elif not in_quotes and char == ',' and paren_count == 0:
                    parts.append(current_part.strip())
                    current_part = ""
                    i += 1
                    continue
                
                current_part += char
                i += 1
            
            if current_part.strip():
                parts.append(current_part.strip())
            
            if len(parts) >= 7:
                # Clean up the values
                id_val = parts[0].strip().strip("'")
                name = parts[1].strip().strip("'").replace("''", "'")
                markdown = parts[2].strip()
                if markdown.startswith("'") and markdown.endswith("'"):
                    markdown = markdown[1:-1].replace("''", "'")
                folder_id = parts[3].strip().strip("'")
                created_at = parts[4].strip().strip("'")
                sort_order = int(parts[5].strip())
                text = parts[6].strip()
                if text == 'NULL':
                    text = None
                else:
                    text = text.strip("'").replace("''", "'")
                
                audio_url = None
                words = None
                if len(parts) > 7:
                    audio_url = parts[7].strip()
                    if audio_url == 'NULL':
                        audio_url = None
                    else:
                        audio_url = audio_url.strip("'")
                
                if len(parts) > 8:
                    words = parts[8].strip()
                    if words == 'NULL':
                        words = None
                    else:
                        words = words.strip("'").replace("''", "'")
                
                wiki_file = {
                    "id": id_val,
                    "name": name,
                    "markdown": markdown,
                    "folder_id": folder_id,
                    "created_at": created_at,
                    "sort_order": sort_order,
                    "text": text,
                    "audio_url": audio_url,
                    "words": words
                }
                wiki_files.append(wiki_file)
        except Exception as e:
            print(f"Error parsing wiki file: {e}")
            continue
    
    return wiki_files

def import_wiki_files_batch(wiki_files, batch_size=10):
    """Import wiki files in batches"""
    headers = {
        "apikey": SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SERVICE_ROLE_KEY}",
        "Content-Type": "application/json"
    }
    
    total = len(wiki_files)
    imported = 0
    
    for i in range(0, total, batch_size):
        batch = wiki_files[i:i+batch_size]
        
        try:
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/wiki_files",
                headers=headers,
                json=batch,
                timeout=30
            )
            
            if response.status_code == 201:
                imported += len(batch)
                print(f"Imported batch {i//batch_size + 1}: {imported}/{total} wiki files")
            else:
                print(f"Error importing batch {i//batch_size + 1}: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"Exception importing batch {i//batch_size + 1}: {e}")
        
        # Small delay between batches
        time.sleep(0.5)
    
    return imported

if __name__ == "__main__":
    print("Parsing wiki_files SQL...")
    wiki_files = parse_wiki_files_sql("import_wiki_files.sql")
    print(f"Found {len(wiki_files)} wiki files")
    
    if wiki_files:
        print("Starting import...")
        imported = import_wiki_files_batch(wiki_files, batch_size=5)
        print(f"Import completed: {imported}/{len(wiki_files)} wiki files imported")
    else:
        print("No wiki files found to import")
