# Инструкции по импорту данных в продакшн Supabase

## Шаг 1: Применение схемы базы данных

1. Откройте SQL Editor в Supabase Dashboard: https://supabase.com/dashboard/project/namplnzyrvdvskwnzpcu/sql
2. Скопируйте содержимое файла `combined_migrations.sql` и вставьте в SQL Editor
3. Выполните SQL запрос для создания всех таблиц, индексов, политик и т.д.

## Шаг 2: Импорт данных

1. Откройте SQL Editor в Supabase Dashboard: https://supabase.com/dashboard/project/namplnzyrvdvskwnzpcu/sql
2. Скопируйте содержимое файла `data_export.sql` и вставьте в SQL Editor
3. Выполните SQL запрос для импорта всех данных

**Примечание**: Если возникают ошибки с циклическими ссылками, можно временно отключить триггеры:

```sql
-- Отключить триггеры перед импортом
ALTER TABLE wiki_folders DISABLE TRIGGER ALL;

-- Вставить данные из data_export.sql

-- Включить триггеры после импорта
ALTER TABLE wiki_folders ENABLE TRIGGER ALL;
```

## Шаг 3: Проверка импорта

После импорта данных проверьте, что все данные успешно импортированы:

```sql
-- Проверка количества записей в таблицах
SELECT 'chats' as table_name, COUNT(*) as count FROM chats
UNION ALL
SELECT 'messages', COUNT(*) FROM messages
UNION ALL
SELECT 'attachments', COUNT(*) FROM attachments
UNION ALL
SELECT 'wiki_folders', COUNT(*) FROM wiki_folders
UNION ALL
SELECT 'wiki_files', COUNT(*) FROM wiki_files
UNION ALL
SELECT 'news', COUNT(*) FROM news;
```

## Шаг 4: Настройка хранилища

Убедитесь, что созданы все необходимые бакеты в Storage:
- `chat-attachments`
- `wiki-audio`

Проверьте, что политики RLS для хранилища настроены правильно.

## Шаг 5: Тестирование приложения

После импорта данных и настройки хранилища протестируйте приложение с новыми настройками подключения к продакшн Supabase.

## Дополнительные команды

### Экспорт данных из локальной базы

```bash
pg_dump "postgresql://postgres:postgres@127.0.0.1:54322/postgres" --data-only --inserts --table=chats --table=messages --table=attachments --table=wiki_folders --table=wiki_files --table=news > data_export.sql
```

### Экспорт схемы из локальной базы

```bash
pg_dump "postgresql://postgres:postgres@127.0.0.1:54322/postgres" --schema-only --table=chats --table=messages --table=attachments --table=wiki_folders --table=wiki_files --table=news > schema_export.sql
```

### Подключение к удалённой базе через CLI

```bash
supabase link --project-ref namplnzyrvdvskwnzpcu
```

### Применение миграций на удалённую базу

```bash
supabase db push
```
