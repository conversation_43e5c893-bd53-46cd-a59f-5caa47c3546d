#!/usr/bin/env python3
import requests

# Supabase configuration
SUPABASE_URL = "https://namplnzyrvdvskwnzpcu.supabase.co"
SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5hbXBsbnp5cnZkdnNrd256cGN1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjA5ODQ5MCwiZXhwIjoyMDY3Njc0NDkwfQ.sm8w-MJxL-uM-u7bYwLph-4_Dbm_eryer-XIDO9dDic"

def get_table_count(table_name):
    """Get count of records in a table"""
    headers = {
        "apikey": SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SERVICE_ROLE_KEY}",
        "Prefer": "count=exact"
    }
    
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/{table_name}?select=count",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            return data[0]['count'] if data else 0
        else:
            return f"Error: {response.status_code}"
            
    except Exception as e:
        return f"Error: {e}"

def main():
    print("📊 Проверка итогового состояния продакшн базы данных:")
    print("=" * 60)
    
    tables = [
        ('chats', 'Чаты'),
        ('messages', 'Сообщения'),
        ('attachments', 'Вложения'),
        ('news', 'Новости'),
        ('wiki_folders', 'Папки Wiki'),
        ('wiki_files', 'Файлы Wiki')
    ]
    
    total_records = 0
    
    for table_name, display_name in tables:
        count = get_table_count(table_name)
        print(f"{display_name:20} | {count:>10}")
        if isinstance(count, int):
            total_records += count
    
    print("=" * 60)
    print(f"{'ИТОГО записей':20} | {total_records:>10}")
    print("\n✅ Миграция данных завершена успешно!")

if __name__ == "__main__":
    main()
