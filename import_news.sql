-- Data for Name: news; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.news VALUES (4, '# Привет!

Это наше приложение, тут:
- База знаний
- Новости
- ИИ ассистент', '2025-04-14 06:47:38.408121+00');
INSERT INTO public.news VALUES (5, '# App Update

- Готова более стабильно работающая версия приложения для macOS
- Версия для Android тоже должна быть в порядке, кто проверит напишите Звонко К.
- Версия под Windows будет в ближайшем будущем.
- Версия для iOS будет после завершения регистрации в Apple. (В процессе...)

---

Добавлена возможность голосового ввода. Для транскрипции голоса в текст. Используется облачный сервис, с очень высоким качеством распознания речи, включая термины на разных языках, и автоматической расстановкой знаков препинания (по смыслу).', '2025-04-20 13:06:26.895582+00');
INSERT INTO public.news VALUES (6, '##  PWA 0.2.1

Версия приложения которая открывается через [app.lsound.info](http://app.lsound.info) (Progressive Web App) улучшена!

- Чаты работают стабильнее.
- Голосовой ввод работает. (нюансы есть, но работает)
-  Запоминание позиции прокрутки Wiki и Аудио работает. (Чтобы удобнее было читать или слушать длинные материалы)

*Скоро добавлю очень мощные книги по свету и звуку.*', '2025-04-28 20:01:20.124419+00');
INSERT INTO public.news VALUES (7, '## PWA 0.3

- Приложение лучше отображается на больших экранах.
- Ответы ИИ отображаются в реальной времени (посимвольно а не ответ целиком)
- Можно отправлять изображения в чат с ИИ', '2025-06-17 13:25:32.398652+00');
INSERT INTO public.news VALUES (9, '## Версия для iPhone
https://testflight.apple.com/join/VmXfXCXt', '2025-07-02 20:31:32.005554+00');


--
