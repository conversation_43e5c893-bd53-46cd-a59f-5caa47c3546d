-- Production Data Import for LuxuryApp
-- Execute this script in Supabase SQL Editor
-- https://supabase.com/dashboard/project/namplnzyrvdvskwnzpcu/sql

-- Disable foreign key checks temporarily for easier import
SET session_replication_role = replica;

-- ============================================================================
-- STEP 1: Import chats (independent table)
-- ============================================================================

-- Data for Name: chats; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.chats VALUES (1, 'Тестовый чат 12:06:43', 'c8a28135-73c1-4262-a1cf-8bc684bafe2a', '2025-07-03 09:06:43.769426+00', '2025-07-03 09:06:43.769426+00');
INSERT INTO public.chats VALUES (2, '03.07.2025, 12:33:12', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-03 09:33:12.549169+00', '2025-07-03 09:33:12.549169+00');
INSERT INTO public.chats VALUES (3, '03.07.2025, 17:47:23', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-03 14:47:23.75104+00', '2025-07-03 14:47:23.75104+00');
INSERT INTO public.chats VALUES (4, '03.07.2025, 18:30:53', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-03 15:30:53.244699+00', '2025-07-03 15:30:53.244699+00');
INSERT INTO public.chats VALUES (5, '03.07.2025, 19:41:42', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-03 16:41:42.825056+00', '2025-07-03 16:41:42.825056+00');
INSERT INTO public.chats VALUES (9, 'Новый чат', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-03 18:24:25.562948+00', '2025-07-03 18:24:25.562948+00');
INSERT INTO public.chats VALUES (10, 'Новый чат', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-03 18:28:02.295894+00', '2025-07-03 18:28:02.295894+00');
INSERT INTO public.chats VALUES (11, 'Новый чат', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-03 20:35:44.756977+00', '2025-07-03 20:35:44.756977+00');
INSERT INTO public.chats VALUES (12, 'Новый чат', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-07 09:35:52.61715+00', '2025-07-07 09:35:52.61715+00');
INSERT INTO public.chats VALUES (13, 'Новый чат', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-07 09:38:52.205163+00', '2025-07-07 09:38:52.205163+00');
INSERT INTO public.chats VALUES (14, 'Новый чат', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-07 09:56:21.49821+00', '2025-07-07 09:56:21.49821+00');
INSERT INTO public.chats VALUES (15, 'Новый чат', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-07 10:04:55.300302+00', '2025-07-07 10:04:55.300302+00');
INSERT INTO public.chats VALUES (16, 'Новый чат', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-07 10:15:43.523289+00', '2025-07-07 10:15:43.523289+00');
INSERT INTO public.chats VALUES (18, 'Есть ли у тебя какие-то инструменты помимо генерац...', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-07 16:56:57.216704+00', '2025-07-07 18:55:47.118974+00');
INSERT INTO public.chats VALUES (17, 'старый чат', '2af86bda-f8b2-495a-b1f3-7e517c6953ff', '2025-07-07 10:25:55.653756+00', '2025-07-10 00:17:02.330162+00');

-- ============================================================================
-- STEP 2: Import news (independent table)
-- ============================================================================

-- Data for Name: news; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.news VALUES (1, '# Добро пожаловать в Luxury App!

Это первая новость в нашем корпоративном приложении.

## Основные функции:
- 🤖 **AI-ассистент** - умный помощник для решения рабочих задач
- 📚 **База знаний** - централизованное хранилище информации компании
- 📰 **Новости** - актуальная информация и обновления

## Возможности AI-ассистента:
- Ответы на вопросы по базе знаний
- Помощь в решении технических задач
- Анализ документов и изображений
- Транскрибация аудио

Приятной работы! 🚀', '2025-07-03 09:00:00+00');
INSERT INTO public.news VALUES (2, '# Обновление системы

Сегодня было выполнено важное обновление нашей системы.

## Что нового:
- Улучшена производительность AI-ассистента
- Добавлена поддержка новых форматов файлов
- Исправлены мелкие ошибки в интерфейсе

## Планы на будущее:
- Интеграция с внешними системами
- Расширение базы знаний
- Новые инструменты для работы с данными

Спасибо за терпение во время обновления! 💪', '2025-07-03 12:00:00+00');

-- ============================================================================
-- STEP 3: Import wiki_folders (with circular references handling)
-- ============================================================================

-- Temporarily disable triggers for wiki_folders
ALTER TABLE wiki_folders DISABLE TRIGGER ALL;

-- Data for Name: wiki_folders; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.wiki_folders VALUES ('550e8400-e29b-41d4-a716-446655440000', 'Документация', NULL, '2025-07-03 09:00:00+00', '📚', '📖', 1);
INSERT INTO public.wiki_folders VALUES ('550e8400-e29b-41d4-a716-446655440001', 'Техническая документация', '550e8400-e29b-41d4-a716-446655440000', '2025-07-03 09:01:00+00', '⚙️', '🔧', 1);
INSERT INTO public.wiki_folders VALUES ('550e8400-e29b-41d4-a716-446655440002', 'Пользовательские руководства', '550e8400-e29b-41d4-a716-446655440000', '2025-07-03 09:02:00+00', '👥', '📋', 2);
INSERT INTO public.wiki_folders VALUES ('550e8400-e29b-41d4-a716-446655440003', 'Процедуры и регламенты', NULL, '2025-07-03 09:03:00+00', '📋', '✅', 2);
INSERT INTO public.wiki_folders VALUES ('550e8400-e29b-41d4-a716-446655440004', 'Безопасность', '550e8400-e29b-41d4-a716-446655440003', '2025-07-03 09:04:00+00', '🔒', '🛡️', 1);

-- Re-enable triggers for wiki_folders
ALTER TABLE wiki_folders ENABLE TRIGGER ALL;
