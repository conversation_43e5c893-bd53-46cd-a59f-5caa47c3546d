#!/usr/bin/env python3
import psycopg2
import requests
import json
import time

# Database connections
LOCAL_DB = "postgresql://postgres:postgres@127.0.0.1:54322/postgres"
SUPABASE_URL = "https://namplnzyrvdvskwnzpcu.supabase.co"
SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5hbXBsbnp5cnZkdnNrd256cGN1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjA5ODQ5MCwiZXhwIjoyMDY3Njc0NDkwfQ.sm8w-MJxL-uM-u7bYwLph-4_Dbm_eryer-XIDO9dDic"

def get_wiki_files_from_local():
    """Get all wiki files from local database"""
    try:
        conn = psycopg2.connect(LOCAL_DB)
        cur = conn.cursor()
        
        cur.execute("""
            SELECT id, name, markdown, folder_id, created_at, sort_order, text, audio_url, words 
            FROM wiki_files 
            ORDER BY created_at
        """)
        
        columns = [desc[0] for desc in cur.description]
        wiki_files = []
        
        for row in cur.fetchall():
            wiki_file = dict(zip(columns, row))
            # Convert datetime to string
            if wiki_file['created_at']:
                wiki_file['created_at'] = wiki_file['created_at'].isoformat()
            wiki_files.append(wiki_file)
        
        cur.close()
        conn.close()
        
        return wiki_files
    except Exception as e:
        print(f"Error reading from local database: {e}")
        return []

def import_wiki_file(wiki_file):
    """Import a single wiki file to Supabase"""
    headers = {
        "apikey": SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SERVICE_ROLE_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/wiki_files",
            headers=headers,
            json=[wiki_file],  # Send as array
            timeout=120
        )
        
        if response.status_code == 201:
            return True, None
        else:
            return False, f"HTTP {response.status_code}: {response.text[:200]}"
            
    except Exception as e:
        return False, str(e)

def main():
    print("🔄 Reading wiki files from local database...")
    wiki_files = get_wiki_files_from_local()
    
    if not wiki_files:
        print("❌ No wiki files found in local database")
        return
    
    print(f"📊 Found {len(wiki_files)} wiki files to import")
    
    imported = 0
    failed = 0
    
    for i, wiki_file in enumerate(wiki_files, 1):
        print(f"📤 Importing {i}/{len(wiki_files)}: {wiki_file['name'][:50]}...")
        
        success, error = import_wiki_file(wiki_file)
        
        if success:
            imported += 1
            print(f"✅ Success")
        else:
            failed += 1
            print(f"❌ Failed: {error}")
        
        # Small delay between requests
        time.sleep(0.5)
        
        # Progress update every 10 files
        if i % 10 == 0:
            print(f"📊 Progress: {imported} imported, {failed} failed")
    
    print(f"\n🎉 Import completed!")
    print(f"✅ Successfully imported: {imported}/{len(wiki_files)} wiki files")
    print(f"❌ Failed: {failed}/{len(wiki_files)} wiki files")

if __name__ == "__main__":
    main()
